from rest_framework import viewsets
from rest_framework.permissions import IsAdminUser
from cls_backend.models import User
from cls_backend.modules.user.serializers import UserSerializer
from organization.models import OrganizationMember
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status as drf_status
from organization.models import Organization, OrganizationMember
class UserViewSet(viewsets.ModelViewSet):
    def perform_create(self, serializer):
        user = serializer.save()
        # Kiểm tra user có thuộc tổ chức nào không
        
        default_org, _ = Organization.objects.get_or_create(name="Default Organization")
        is_member = OrganizationMember.objects.filter(user=user).exists()
        if not is_member:
            OrganizationMember.objects.create(user=user, organization=default_org, is_active=True)


    @action(detail=True, methods=['post'], url_path='active')
    def active(self, request, pk=None):
        """Kích hoạt user và tất cả membership trong các tổ chức"""
        user = self.get_object()

        # Cậ<PERSON> nhật is_active của User
        user.is_active = True
        user.save()

        # Cập nhật is_active của tất cả OrganizationMember
        org_members_updated = OrganizationMember.objects.filter(user=user).update(is_active=True)

        return Response({
            "success": True,
            "message": f"User {user.email} và {org_members_updated} membership đã được kích hoạt",
            "user_is_active": user.is_active,
            "org_members_updated": org_members_updated
        }, status=drf_status.HTTP_200_OK)

    @action(detail=True, methods=['post'], url_path='deactive')
    def deactive(self, request, pk=None):
        """Vô hiệu hóa user và tất cả membership trong các tổ chức"""
        user = self.get_object()

        # Cập nhật is_active của User
        user.is_active = False
        user.save()

        # Cập nhật is_active của tất cả OrganizationMember
        org_members_updated = OrganizationMember.objects.filter(user=user).update(is_active=False)

        return Response({
            "success": True,
            "message": f"User {user.email} và {org_members_updated} membership đã được vô hiệu hóa",
            "user_is_active": user.is_active,
            "org_members_updated": org_members_updated
        }, status=drf_status.HTTP_200_OK)
    """
    API cho phép superuser quản lý người dùng (CRUD)
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAdminUser]

    def get_queryset(self):
        queryset = User.objects.all()
        organization_id = self.request.query_params.get('organization_id')
        status = self.request.query_params.get('active')
        search = self.request.query_params.get('search')

        if organization_id or status:
            org_member_filter = {}
            if organization_id:
                org_member_filter['organization_id'] = organization_id
            if status:
                org_member_filter['is_active'] = status
            user_ids = OrganizationMember.objects.filter(**org_member_filter).values_list('user_id', flat=True)
            queryset = queryset.filter(id__in=user_ids)

        if search:
            queryset = queryset.filter(fullname__icontains=search) | queryset.filter(email__icontains=search)

        return queryset

    def list(self, request, *args, **kwargs):
        page = request.query_params.get('page')
        if page is None:
            self.pagination_class = None
        return super().list(request, *args, **kwargs)
