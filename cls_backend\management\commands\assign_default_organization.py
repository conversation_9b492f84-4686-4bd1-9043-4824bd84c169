from django.core.management.base import BaseCommand
from cls_backend.models import User
from organization.models import Organization, OrganizationMember

class Command(BaseCommand):
    help = "Thêm tất cả các tài khoản chưa có organization vào Default Organization"

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Chỉ hiển thị những gì sẽ được thực hiện mà không thực sự thay đổi dữ liệu',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # Tạo hoặc lấy Default Organization
        default_org, created = Organization.objects.get_or_create(
            name="Default Organization",
            defaults={
                'description': 'Tổ chức mặc định cho các tài khoản chưa được phân bổ',
                'status': 'active'
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f"Đã tạo Default Organization mới: {default_org.name}")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f"Sử dụng Default Organization hiện có: {default_org.name}")
            )

        # Tìm tất cả user chưa có organization (không có bản ghi trong OrganizationMember)
        users_without_org = User.objects.filter(
            organization_memberships__isnull=True,
            is_active=True
        ).distinct()

        total_users = users_without_org.count()
        
        if total_users == 0:
            self.stdout.write(
                self.style.SUCCESS("Tất cả người dùng đều đã có organization!")
            )
            return

        self.stdout.write(
            self.style.WARNING(f"Tìm thấy {total_users} tài khoản chưa có organization:")
        )

        # Hiển thị danh sách user sẽ được thêm
        for user in users_without_org:
            self.stdout.write(f"  - {user.email} ({user.fullname})")

        if dry_run:
            self.stdout.write(
                self.style.WARNING("DRY RUN: Không có thay đổi nào được thực hiện.")
            )
            return

        # Tạo OrganizationMember cho các user chưa có organization
        created_count = 0
        for user in users_without_org:
            try:
                OrganizationMember.objects.create(
                    user=user,
                    organization=default_org,
                    role='member',
                    is_active=True
                )
                created_count += 1
                self.stdout.write(f"✓ Đã thêm {user.email} vào {default_org.name}")
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"✗ Lỗi khi thêm {user.email}: {str(e)}")
                )

        self.stdout.write(
            self.style.SUCCESS(f"Hoàn thành! Đã thêm {created_count}/{total_users} tài khoản vào Default Organization.")
        )
